#include "csvreader.h"
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QDebug>
#include <QStandardPaths>
#include <QCoreApplication>

CsvReader::CsvReader(QObject *parent)
    : QObject(parent)
{
}

QVariantList CsvReader::readDataByDate(const QString &boilerName, const QDate &date)
{
    QVariantList result;
    QString filePath = buildCsvFilePath(boilerName, date);
    
    if (!isValidCsvFile(filePath)) {
        qDebug() << "CSV file does not exist or cannot be read:" << filePath;
        return result;
    }
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "Cannot open CSV file:" << filePath;
        return result;
    }
    
    QTextStream in(&file);
    // Qt 6中默认使用UTF-8编码，无需手动设置
    
    // 跳过表头
    if (!in.atEnd()) {
        in.readLine();
    }
    
    // 读取数据行
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (!line.isEmpty()) {
            QVariantMap dataPoint = parseCsvLine(line);
            if (!dataPoint.isEmpty()) {
                result.append(dataPoint);
            }
        }
    }
    
    file.close();
    qDebug() << "Successfully read CSV data," << result.size() << "records, file:" << filePath;
    return result;
}

QVariantList CsvReader::readDataByDateRange(const QString &boilerName, const QDate &startDate, const QDate &endDate)
{
    QVariantList result;
    
    QDate currentDate = startDate;
    while (currentDate <= endDate) {
        QVariantList dayData = readDataByDate(boilerName, currentDate);
        result.append(dayData);
        currentDate = currentDate.addDays(1);
    }
    
    return result;
}

QStringList CsvReader::getAvailableDates(const QString &boilerName)
{
    QStringList dates;

    // 获取data目录路径
    QString dataDir = QCoreApplication::applicationDirPath() + "/data";
    QDir dir(dataDir);

    qDebug() << "Application dir path:" << QCoreApplication::applicationDirPath();
    qDebug() << "Current working directory:" << QDir::currentPath();
    qDebug() << "Looking for data in directory:" << dataDir;
    qDebug() << "Directory exists:" << dir.exists();
    qDebug() << "Searching for boiler:" << boilerName.toUtf8();

    if (!dir.exists()) {
        qDebug() << "Data directory does not exist:" << dataDir;
        // 尝试从当前工作目录查找
        QString currentDataDir = QDir::currentPath() + "/data";
        QDir currentDir(currentDataDir);
        qDebug() << "Trying current working directory:" << currentDataDir;
        qDebug() << "Current dir exists:" << currentDir.exists();

        if (currentDir.exists()) {
            dataDir = currentDataDir;
            dir = currentDir;
            qDebug() << "Using current working directory for data";
        } else {
            return dates;
        }
    }
    
    // 查找匹配的CSV文件
    // 统一使用 boiler_YYYYMMDD.csv 格式
    QStringList filters;
    filters << "boiler_*.csv";
    qDebug() << "Using unified pattern: boiler_*.csv";

    QFileInfoList fileList = dir.entryInfoList(filters, QDir::Files, QDir::Name);
    qDebug() << "Found" << fileList.size() << "files matching pattern";

    for (const QFileInfo &fileInfo : fileList) {
        QString fileName = fileInfo.baseName();
        qDebug() << "Processing file:" << fileName;

        QString dateStr;
        // 格式: boiler_YYYYMMDD.csv
        if (fileName.startsWith("boiler_") && fileName.length() >= 15) { // "boiler_" + 8位日期
            dateStr = fileName.mid(7, 8); // 从第7位开始取8位日期
        }

        qDebug() << "Extracted date string:" << dateStr;

        if (dateStr.length() == 8) {
            QDate date = QDate::fromString(dateStr, "yyyyMMdd");
            if (date.isValid()) {
                QString dateString = date.toString("yyyy-MM-dd");
                qDebug() << "Valid date found:" << dateString;
                if (!dates.contains(dateString)) {
                    dates.append(dateString);
                }
            } else {
                qDebug() << "Invalid date format:" << dateStr;
            }
        }
    }
    
    dates.sort();
    qDebug() << "Final dates list:" << dates;
    return dates;
}

bool CsvReader::hasDataForDate(const QString &boilerName, const QDate &date)
{
    QString filePath = buildCsvFilePath(boilerName, date);
    return isValidCsvFile(filePath);
}

QString CsvReader::buildCsvFilePath(const QString &boilerName, const QDate &date)
{
    QString dataDir = QCoreApplication::applicationDirPath() + "/data";
    QDir dir(dataDir);

    // 如果应用程序目录下没有data文件夹，尝试当前工作目录
    if (!dir.exists()) {
        QString currentDataDir = QDir::currentPath() + "/data";
        QDir currentDir(currentDataDir);
        if (currentDir.exists()) {
            dataDir = currentDataDir;
        }
    }

    QString dateStr = date.toString("yyyyMMdd");
    // 统一使用 boiler_YYYYMMDD.csv 格式
    QString fileName = QString("boiler_%1.csv").arg(dateStr);

    QString filePath = QDir(dataDir).absoluteFilePath(fileName);
    qDebug() << "Building CSV file path:" << filePath;
    return filePath;
}

QVariantMap CsvReader::parseCsvLine(const QString &line)
{
    QVariantMap result;

    QStringList fields = line.split(',');
    // 期望字段数：时间戳(1) + 烟气分析仪数据(5) + DCS数据(6) = 12个字段
    if (fields.size() < 12) {
        qDebug() << "CSV行格式不正确，字段数不足:" << line << "，实际字段数:" << fields.size() << "，期望字段数:12";
        return result;
    }

    // CSV格式: 时间戳,O2,CO,测点温度,电压,电流,炉膛实际温度,实际炉压,给煤量,实际生料量1,实际生料量2,引风机频率
    // 注意：与csvfile.cpp中的写入格式保持完全一致
    bool ok;
    qint64 timestamp = fields[0].toLongLong(&ok);
    if (!ok) {
        qDebug() << "时间戳解析失败:" << fields[0];
        return result;
    }

    QDateTime dateTime = QDateTime::fromSecsSinceEpoch(timestamp);

    result["timestamp"] = timestamp;
    result["datetime"] = dateTime.toString("yyyy-MM-dd hh:mm:ss");
    result["time"] = dateTime.toString("hh:mm:ss");

    // 烟气分析仪数据
    result["o2"] = fields[1].toDouble();
    result["co"] = fields[2].toDouble();
    result["temperature"] = fields[3].toDouble();
    result["voltage"] = fields[4].toDouble();
    result["current"] = fields[5].toDouble();

    // DCS数据
    result["furnace_temp"] = fields[6].toDouble();
    result["furnace_pressure"] = fields[7].toDouble();
    result["coal_feed"] = fields[8].toDouble();
    result["raw_material1"] = fields[9].toDouble();
    result["raw_material2"] = fields[10].toDouble();
    result["fan_speed"] = fields[11].toDouble();



    return result;
}

bool CsvReader::isValidCsvFile(const QString &filePath)
{
    QFile file(filePath);
    return file.exists() && file.size() > 0;
}

// ==================== 新增：专门用于图表的高效数据读取方法 ====================

QVariantList CsvReader::readChartDataForToday(const QString &boilerName)
{
    QDate today = QDate::currentDate();
    return readChartDataByDate(boilerName, today);
}

QVariantList CsvReader::readChartDataByDate(const QString &boilerName, const QDate &date)
{
    QString filePath = buildCsvFilePath(boilerName, date);

    if (!isValidCsvFile(filePath)) {
        qDebug() << "CSV file does not exist for chart data:" << filePath;
        return QVariantList();
    }

    // 使用优化的解析方法
    QList<ChartDataPoint> dataPoints = parseChartDataOptimized(filePath);
    return convertToQmlFormat(dataPoints);
}

QVariantList CsvReader::readRecentChartData(const QString &boilerName, int maxPoints)
{
    QDate today = QDate::currentDate();
    QString filePath = buildCsvFilePath(boilerName, today);

    if (!isValidCsvFile(filePath)) {
        qDebug() << "No CSV file found for recent chart data:" << filePath;
        return QVariantList();
    }

    // 读取最近的数据点
    QList<ChartDataPoint> dataPoints = parseChartDataOptimized(filePath, maxPoints);
    return convertToQmlFormat(dataPoints);
}

QVariantList CsvReader::readChartDataByTimeRange(const QString &boilerName, const QDateTime &startTime, const QDateTime &endTime)
{
    QVariantList result;

    // 确定需要读取的日期范围
    QDate startDate = startTime.date();
    QDate endDate = endTime.date();

    QDate currentDate = startDate;
    while (currentDate <= endDate) {
        QString filePath = buildCsvFilePath(boilerName, currentDate);

        if (isValidCsvFile(filePath)) {
            QList<ChartDataPoint> dayData = parseChartDataOptimized(filePath);

            // 过滤时间范围
            for (const ChartDataPoint &point : dayData) {
                QDateTime pointTime = QDateTime::fromSecsSinceEpoch(point.timestamp);
                if (pointTime >= startTime && pointTime <= endTime) {
                    QVariantMap dataPoint;
                    dataPoint["timestamp"] = point.timestamp;
                    dataPoint["x"] = point.relativeTimeHours;
                    dataPoint["x_minutes"] = point.relativeTimeMinutes;
                    dataPoint["o2"] = point.o2;
                    dataPoint["co"] = point.co;
                    result.append(dataPoint);
                }
            }
        }

        currentDate = currentDate.addDays(1);
    }

    qDebug() << "Read chart data by time range:" << result.size() << "points from" << startTime << "to" << endTime;
    return result;
}

QString CsvReader::getCurrentCsvFilePath(const QString &boilerName)
{
    QDate today = QDate::currentDate();
    return buildCsvFilePath(boilerName, today);
}

// 高效的图表数据解析方法 - 只提取O2和CO数据
QVariantMap CsvReader::parseChartDataLine(const QString &line, qint64 baseTimestamp)
{
    QVariantMap result;

    QStringList fields = line.split(',');
    if (fields.size() < 3) {  // 至少需要时间戳、O2、CO
        return result;
    }

    bool ok;
    qint64 timestamp = fields[0].toLongLong(&ok);
    if (!ok) {
        return result;
    }

    double o2 = fields[1].toDouble();
    double co = fields[2].toDouble();

    // 计算相对时间（如果提供了基准时间戳）
    double relativeTimeHours = 0.0;
    double relativeTimeMinutes = 0.0;

    if (baseTimestamp > 0) {
        qint64 elapsedSeconds = timestamp - baseTimestamp;
        relativeTimeHours = elapsedSeconds / 3600.0;
        relativeTimeMinutes = elapsedSeconds / 60.0;
    }

    result["timestamp"] = timestamp;
    result["x"] = relativeTimeHours;
    result["x_minutes"] = relativeTimeMinutes;
    result["o2"] = o2;
    result["co"] = co;

    return result;
}

// 优化的批量数据解析 - 专门为图表设计
QList<CsvReader::ChartDataPoint> CsvReader::parseChartDataOptimized(const QString &filePath, int maxPoints)
{
    QList<ChartDataPoint> result;

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "Cannot open CSV file for chart data:" << filePath;
        return result;
    }

    QTextStream in(&file);

    // 跳过表头
    if (!in.atEnd()) {
        in.readLine();
    }

    QStringList lines;

    // 如果指定了最大点数，从文件末尾开始读取
    if (maxPoints > 0) {
        // 读取所有行到内存
        while (!in.atEnd()) {
            QString line = in.readLine().trimmed();
            if (!line.isEmpty()) {
                lines.append(line);
            }
        }

        // 如果行数超过最大点数，只取最后的部分
        if (lines.size() > maxPoints) {
            lines = lines.mid(lines.size() - maxPoints);
        }
    } else {
        // 读取所有数据
        while (!in.atEnd()) {
            QString line = in.readLine().trimmed();
            if (!line.isEmpty()) {
                lines.append(line);
            }
        }
    }

    file.close();

    // 解析数据并计算相对时间
    qint64 baseTimestamp = 0;

    for (int i = 0; i < lines.size(); ++i) {
        const QString &line = lines[i];
        QStringList fields = line.split(',');

        if (fields.size() < 3) {
            continue;
        }

        bool ok;
        qint64 timestamp = fields[0].toLongLong(&ok);
        if (!ok) {
            continue;
        }

        // 设置基准时间戳（第一个有效数据点的时间）
        if (baseTimestamp == 0) {
            baseTimestamp = timestamp;
        }

        ChartDataPoint point;
        point.timestamp = timestamp;
        point.o2 = fields[1].toDouble();
        point.co = fields[2].toDouble();

        // 计算相对时间
        qint64 elapsedSeconds = timestamp - baseTimestamp;
        point.relativeTimeHours = elapsedSeconds / 3600.0;
        point.relativeTimeMinutes = elapsedSeconds / 60.0;

        result.append(point);
    }

    qDebug() << "Parsed chart data:" << result.size() << "points from" << filePath;
    return result;
}

// 将内部数据结构转换为QML兼容格式
QVariantList CsvReader::convertToQmlFormat(const QList<ChartDataPoint> &dataPoints)
{
    QVariantList result;
    result.reserve(dataPoints.size());

    for (const ChartDataPoint &point : dataPoints) {
        QVariantMap dataPoint;
        dataPoint["timestamp"] = point.timestamp;
        dataPoint["x"] = point.relativeTimeHours;
        dataPoint["x_minutes"] = point.relativeTimeMinutes;
        dataPoint["o2"] = point.o2;
        dataPoint["co"] = point.co;
        result.append(dataPoint);
    }

    return result;
}
